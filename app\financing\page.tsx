"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Calculator, 
  CreditCard, 
  CheckCircle, 
  DollarSign,
  Percent,
  Calendar,
  TrendingUp,
  Shield,
  Clock,
  Award
} from "lucide-react"

export default function FinancingPage() {
  const [loanAmount, setLoanAmount] = useState([25000])
  const [downPayment, setDownPayment] = useState([5000])
  const [interestRate, setInterestRate] = useState([4.5])
  const [loanTerm, setLoanTerm] = useState([60])
  const [monthlyPayment, setMonthlyPayment] = useState(0)
  const [totalInterest, setTotalInterest] = useState(0)
  const [totalPayment, setTotalPayment] = useState(0)

  // Calculate loan payments
  useEffect(() => {
    const principal = loanAmount[0] - downPayment[0]
    const monthlyRate = interestRate[0] / 100 / 12
    const numPayments = loanTerm[0]

    if (principal > 0 && monthlyRate > 0) {
      const monthly = (principal * monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                     (Math.pow(1 + monthlyRate, numPayments) - 1)
      const total = monthly * numPayments
      const interest = total - principal

      setMonthlyPayment(monthly)
      setTotalPayment(total)
      setTotalInterest(interest)
    } else {
      setMonthlyPayment(0)
      setTotalPayment(0)
      setTotalInterest(0)
    }
  }, [loanAmount, downPayment, interestRate, loanTerm])

  const benefits = [
    {
      icon: CreditCard,
      title: "Flexible Terms",
      description: "Choose from 24 to 84-month financing terms to fit your budget."
    },
    {
      icon: Percent,
      title: "Competitive Rates",
      description: "Get rates as low as 2.9% APR for qualified buyers."
    },
    {
      icon: Clock,
      title: "Quick Approval",
      description: "Get pre-approved in minutes with our streamlined process."
    },
    {
      icon: Shield,
      title: "Bad Credit OK",
      description: "We work with all credit types to find financing solutions."
    }
  ]

  const loanOptions = [
    {
      title: "Excellent Credit",
      creditRange: "750+",
      rate: "2.9% - 4.9%",
      features: ["Lowest rates available", "Flexible terms", "No prepayment penalty"],
      badge: "Best Rate"
    },
    {
      title: "Good Credit",
      creditRange: "650-749",
      rate: "4.9% - 7.9%",
      features: ["Competitive rates", "Multiple term options", "Quick approval"],
      badge: "Popular"
    },
    {
      title: "Fair Credit",
      creditRange: "550-649",
      rate: "7.9% - 12.9%",
      features: ["Credit building opportunity", "Flexible down payments", "Personal service"],
      badge: null
    },
    {
      title: "Building Credit",
      creditRange: "Below 550",
      rate: "12.9% - 18.9%",
      features: ["Second chance financing", "Credit improvement program", "Dedicated support"],
      badge: "Fresh Start"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-green-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <Badge className="bg-white/20 text-white border-white/30">
              Auto Financing
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold">
              Financing Made Simple
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              Get pre-approved for your dream car with competitive rates and flexible terms. 
              Our financing experts make the process quick and easy.
            </p>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Why Finance with Baligh Motors?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We make car financing simple, transparent, and accessible for everyone.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                  <benefit.icon className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Calculator Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Calculator */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900 flex items-center gap-2">
                  <Calculator className="h-6 w-6 text-green-600" />
                  Loan Calculator
                </CardTitle>
                <p className="text-gray-600">
                  Calculate your monthly payments and see how different terms affect your budget.
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Vehicle Price */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Vehicle Price: ${loanAmount[0].toLocaleString()}
                  </Label>
                  <Slider
                    value={loanAmount}
                    onValueChange={setLoanAmount}
                    max={100000}
                    min={5000}
                    step={1000}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>$5,000</span>
                    <span>$100,000</span>
                  </div>
                </div>

                {/* Down Payment */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Down Payment: ${downPayment[0].toLocaleString()}
                  </Label>
                  <Slider
                    value={downPayment}
                    onValueChange={setDownPayment}
                    max={Math.min(loanAmount[0] * 0.5, 25000)}
                    min={0}
                    step={500}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>$0</span>
                    <span>${Math.min(loanAmount[0] * 0.5, 25000).toLocaleString()}</span>
                  </div>
                </div>

                {/* Interest Rate */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Interest Rate: {interestRate[0]}% APR
                  </Label>
                  <Slider
                    value={interestRate}
                    onValueChange={setInterestRate}
                    max={20}
                    min={1}
                    step={0.1}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1%</span>
                    <span>20%</span>
                  </div>
                </div>

                {/* Loan Term */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Loan Term: {loanTerm[0]} months
                  </Label>
                  <Slider
                    value={loanTerm}
                    onValueChange={setLoanTerm}
                    max={84}
                    min={24}
                    step={12}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>24 months</span>
                    <span>84 months</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900 flex items-center gap-2">
                  <DollarSign className="h-6 w-6 text-green-600" />
                  Payment Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Monthly Payment */}
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <div className="text-sm text-green-600 font-medium mb-2">Estimated Monthly Payment</div>
                  <div className="text-4xl font-bold text-green-600">
                    ${monthlyPayment.toFixed(2)}
                  </div>
                </div>

                <Separator />

                {/* Payment Details */}
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Loan Amount</span>
                    <span className="font-semibold">${(loanAmount[0] - downPayment[0]).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Down Payment</span>
                    <span className="font-semibold">${downPayment[0].toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Interest Rate</span>
                    <span className="font-semibold">{interestRate[0]}% APR</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Loan Term</span>
                    <span className="font-semibold">{loanTerm[0]} months</span>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Interest</span>
                    <span className="font-semibold text-orange-600">${totalInterest.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Payment</span>
                    <span className="font-semibold">${totalPayment.toFixed(2)}</span>
                  </div>
                </div>

                <Button className="w-full bg-green-600 hover:bg-green-700 text-lg py-3">
                  Get Pre-Approved
                </Button>
                
                <p className="text-xs text-gray-500 text-center">
                  *Estimated payments. Actual rates may vary based on credit approval.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Loan Options */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Financing Options for Every Credit Score
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We work with all credit types to find the right financing solution for you.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {loanOptions.map((option, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow relative">
                {option.badge && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className={`${
                      option.badge === "Best Rate" ? "bg-green-600" :
                      option.badge === "Popular" ? "bg-blue-600" :
                      "bg-orange-600"
                    } text-white`}>
                      {option.badge}
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-xl text-gray-900">{option.title}</CardTitle>
                  <div className="text-sm text-gray-500">Credit Score: {option.creditRange}</div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{option.rate}</div>
                    <div className="text-sm text-gray-500">APR Range</div>
                  </div>
                  
                  <ul className="space-y-2">
                    {option.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2 text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button variant="outline" className="w-full border-green-600 text-green-600 hover:bg-green-50">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-600 rounded-full">
              <TrendingUp className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold">Ready to Get Pre-Approved?</h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Get started with our quick and easy pre-approval process. No impact to your credit score 
              for the initial application, and you'll have an answer in minutes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-green-600 hover:bg-green-700 text-lg px-8 py-3">
                Apply for Financing
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900 text-lg px-8 py-3">
                Speak with Expert
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
