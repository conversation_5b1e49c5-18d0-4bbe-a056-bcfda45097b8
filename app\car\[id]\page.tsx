"use client"

import { useState } from "react"
import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Heart, 
  Share2, 
  Phone, 
  Calendar,
  Fuel,
  Settings,
  Users,
  Gauge,
  Shield,
  Award,
  CheckCircle,
  Star,
  ArrowLeft
} from "lucide-react"
import Image from "next/image"

// This would typically come from a database or API
const carData = {
  id: "1",
  name: "Camry",
  brand: "Toyota",
  year: 2023,
  price: 28500,
  mileage: 15000,
  fuelType: "Hybrid",
  transmission: "Automatic",
  seats: 5,
  condition: "used" as const,
  featured: true,
  images: [
    "/placeholder.jpg",
    "/placeholder.jpg",
    "/placeholder.jpg",
    "/placeholder.jpg"
  ],
  specifications: {
    engine: "2.5L 4-Cylinder Hybrid",
    horsepower: "208 hp",
    mpg: "52 city / 48 highway",
    drivetrain: "Front-Wheel Drive",
    exterior: "Midnight Black Metallic",
    interior: "Black Fabric",
    vin: "4T1C11AK5NU123456",
    stockNumber: "*********"
  },
  features: [
    "Toyota Safety Sense 2.0",
    "Apple CarPlay & Android Auto",
    "Wireless Phone Charging",
    "Dual-Zone Climate Control",
    "LED Headlights",
    "Backup Camera",
    "Blind Spot Monitoring",
    "Lane Departure Alert",
    "Adaptive Cruise Control",
    "Push Button Start"
  ],
  description: "This 2023 Toyota Camry Hybrid combines exceptional fuel efficiency with reliable performance. Featuring Toyota's renowned hybrid technology, this vehicle offers an impressive 50+ MPG while maintaining the comfort and features you expect from a midsize sedan.",
  warranty: "Remaining factory warranty plus our 30-day/1,000-mile guarantee",
  history: "One owner, clean CARFAX report, regular maintenance records available"
}

export default function CarDetailsPage() {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isFavorited, setIsFavorited] = useState(false)

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  }

  const formatMileage = (mileage: number) => {
    return new Intl.NumberFormat('en-US').format(mileage)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Button variant="ghost" size="sm" className="p-0 h-auto">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Inventory
            </Button>
            <span>/</span>
            <span>{carData.brand}</span>
            <span>/</span>
            <span className="text-gray-900">{carData.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card className="border-0 shadow-lg overflow-hidden">
              <div className="relative">
                <Image
                  src={carData.images[selectedImage]}
                  alt={`${carData.brand} ${carData.name}`}
                  width={800}
                  height={500}
                  className="w-full h-96 object-cover"
                />
                
                {/* Badges */}
                <div className="absolute top-4 left-4 flex gap-2">
                  {carData.featured && (
                    <Badge className="bg-yellow-500 text-black font-semibold">
                      Featured
                    </Badge>
                  )}
                  <Badge 
                    className={
                      carData.condition === "new" 
                        ? "bg-green-600 text-white" 
                        : carData.condition === "certified" 
                        ? "bg-blue-600 text-white" 
                        : "bg-gray-600 text-white"
                    }
                  >
                    {carData.condition.charAt(0).toUpperCase() + carData.condition.slice(1)}
                  </Badge>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-4 right-4 flex gap-2">
                  <Button 
                    size="sm" 
                    variant="secondary" 
                    className="bg-white/90 hover:bg-white"
                    onClick={() => setIsFavorited(!isFavorited)}
                  >
                    <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                  </Button>
                  <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {/* Thumbnail Gallery */}
              <div className="p-4">
                <div className="flex gap-2 overflow-x-auto">
                  {carData.images.map((image, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => setSelectedImage(index)}
                      className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 ${
                        selectedImage === index ? 'border-green-600' : 'border-gray-200'
                      }`}
                      aria-label={`View image ${index + 1}`}
                    >
                      <Image
                        src={image}
                        alt={`View ${index + 1}`}
                        width={80}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            </Card>

            {/* Vehicle Details Tabs */}
            <Card className="border-0 shadow-lg">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="specifications">Specs</TabsTrigger>
                  <TabsTrigger value="features">Features</TabsTrigger>
                  <TabsTrigger value="history">History</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">Vehicle Overview</h3>
                    <p className="text-gray-600 leading-relaxed">{carData.description}</p>
                    
                    <div className="grid md:grid-cols-2 gap-6 pt-4">
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900">Key Specifications</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Engine:</span>
                            <span className="text-gray-900">{carData.specifications.engine}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Horsepower:</span>
                            <span className="text-gray-900">{carData.specifications.horsepower}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">MPG:</span>
                            <span className="text-gray-900">{carData.specifications.mpg}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900">Vehicle Details</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Exterior Color:</span>
                            <span className="text-gray-900">{carData.specifications.exterior}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Interior:</span>
                            <span className="text-gray-900">{carData.specifications.interior}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Stock #:</span>
                            <span className="text-gray-900">{carData.specifications.stockNumber}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="specifications" className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">Technical Specifications</h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Engine:</span>
                          <span className="text-gray-900">{carData.specifications.engine}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Horsepower:</span>
                          <span className="text-gray-900">{carData.specifications.horsepower}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Fuel Economy:</span>
                          <span className="text-gray-900">{carData.specifications.mpg}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Drivetrain:</span>
                          <span className="text-gray-900">{carData.specifications.drivetrain}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Transmission:</span>
                          <span className="text-gray-900">{carData.transmission}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Fuel Type:</span>
                          <span className="text-gray-900">{carData.fuelType}</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">Seating:</span>
                          <span className="text-gray-900">{carData.seats} passengers</span>
                        </div>
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <span className="text-gray-600">VIN:</span>
                          <span className="text-gray-900 text-sm">{carData.specifications.vin}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="features" className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">Features & Equipment</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {carData.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="history" className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">Vehicle History</h3>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <Shield className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">Clean History Report</h4>
                          <p className="text-gray-600">{carData.history}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <Award className="h-6 w-6 text-blue-600 flex-shrink-0 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">Warranty Coverage</h4>
                          <p className="text-gray-600">{carData.warranty}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Price & Actions */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <div className="space-y-2">
                  <h1 className="text-2xl font-bold text-gray-900">
                    {carData.year} {carData.brand} {carData.name}
                  </h1>
                  <div className="text-3xl font-bold text-green-600">
                    {formatPrice(carData.price)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>{carData.year}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Gauge className="h-4 w-4" />
                    <span>{formatMileage(carData.mileage)} mi</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Settings className="h-4 w-4" />
                    <span>{carData.transmission}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Fuel className="h-4 w-4" />
                    <span>{carData.fuelType}</span>
                  </div>
                </div>

                <Separator />

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button className="w-full bg-green-600 hover:bg-green-700 text-lg py-3">
                    <Calendar className="h-5 w-5 mr-2" />
                    Schedule Test Drive
                  </Button>
                  <Button variant="outline" className="w-full border-green-600 text-green-600 hover:bg-green-50">
                    Get Financing Quote
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Sales Team
                  </Button>
                </div>

                <Separator />

                {/* Contact Info */}
                <div className="text-center space-y-2">
                  <div className="text-sm text-gray-600">Questions about this vehicle?</div>
                  <div className="text-lg font-semibold text-gray-900">(*************</div>
                  <div className="text-sm text-gray-500"><EMAIL></div>
                </div>
              </CardContent>
            </Card>

            {/* Financing Calculator */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900">Estimate Your Payment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-sm text-green-600 font-medium mb-1">Estimated Monthly Payment</div>
                  <div className="text-2xl font-bold text-green-600">$425/mo</div>
                  <div className="text-xs text-gray-500 mt-1">Based on 60 months at 4.9% APR</div>
                </div>
                <Button variant="outline" className="w-full">
                  Calculate Your Payment
                </Button>
              </CardContent>
            </Card>

            {/* Reviews */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900">Customer Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">4.9/5 (127 reviews)</span>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  "Excellent experience at Baligh Motors. The team was professional and the car was exactly as described."
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Read All Reviews
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
