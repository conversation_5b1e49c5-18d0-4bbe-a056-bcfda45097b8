import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Wrench, 
  Shield, 
  CreditCard, 
  Car, 
  Clock, 
  CheckCircle,
  Phone,
  Calendar,
  DollarSign,
  FileText,
  Users,
  Award
} from "lucide-react"

export default function ServicesPage() {
  const services = [
    {
      icon: Car,
      title: "Vehicle Sales",
      description: "Browse our extensive inventory of new, certified pre-owned, and used vehicles from top brands.",
      features: [
        "Wide selection of vehicles",
        "Competitive pricing",
        "Vehicle history reports",
        "Quality inspections"
      ],
      cta: "Browse Inventory"
    },
    {
      icon: Wrench,
      title: "Service & Maintenance",
      description: "Keep your vehicle running smoothly with our comprehensive maintenance and repair services.",
      features: [
        "Oil changes & tune-ups",
        "Brake & tire services",
        "Engine diagnostics",
        "Warranty repairs"
      ],
      cta: "Schedule Service"
    },
    {
      icon: CreditCard,
      title: "Financing Solutions",
      description: "Flexible financing options to help you get the vehicle you want with payments that fit your budget.",
      features: [
        "Competitive interest rates",
        "Flexible payment terms",
        "Bad credit financing",
        "Lease options available"
      ],
      cta: "Get Pre-Approved"
    },
    {
      icon: Shield,
      title: "Extended Warranties",
      description: "Protect your investment with comprehensive warranty coverage for peace of mind.",
      features: [
        "Powertrain coverage",
        "Bumper-to-bumper protection",
        "Roadside assistance",
        "Rental car coverage"
      ],
      cta: "Learn More"
    },
    {
      icon: FileText,
      title: "Trade-In Appraisals",
      description: "Get the best value for your current vehicle with our fair and transparent trade-in process.",
      features: [
        "Free vehicle appraisals",
        "Competitive trade values",
        "Instant online quotes",
        "Same-day processing"
      ],
      cta: "Get Quote"
    },
    {
      icon: Users,
      title: "Customer Support",
      description: "Our dedicated support team is here to help with any questions or concerns you may have.",
      features: [
        "24/7 customer support",
        "Expert guidance",
        "After-sale support",
        "Satisfaction guarantee"
      ],
      cta: "Contact Support"
    }
  ]

  const benefits = [
    {
      icon: Award,
      title: "Certified Technicians",
      description: "Our ASE-certified technicians have the expertise to service all makes and models."
    },
    {
      icon: Clock,
      title: "Quick Turnaround",
      description: "Most services completed the same day with convenient scheduling options."
    },
    {
      icon: DollarSign,
      title: "Competitive Pricing",
      description: "Fair, transparent pricing with no hidden fees or surprise charges."
    },
    {
      icon: CheckCircle,
      title: "Quality Guarantee",
      description: "All work backed by our comprehensive warranty and satisfaction guarantee."
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <Badge className="bg-white/20 text-white border-white/30">
              Premium Services
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold">
              Exceptional Services
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              Experience comprehensive automotive excellence with our premium services,
              tailored for discerning clients who demand the finest.
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Complete Automotive Solutions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We offer a comprehensive range of services to meet all your automotive needs, 
              backed by our commitment to quality and customer satisfaction.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow h-full">
                <CardHeader className="text-center pb-4">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                    <service.icon className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-xl text-gray-900">{service.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">{service.description}</p>
                  
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2 text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button className="w-full bg-green-600 hover:bg-green-700 mt-6">
                    {service.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-green-100 text-green-800">Why Choose Us</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              The Baligh Motors Advantage
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Experience the difference that comes with choosing a dealership that 
              truly cares about your satisfaction and success.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                  <benefit.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Hours */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <Badge className="bg-blue-100 text-blue-800">Service Hours</Badge>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Convenient Hours to Serve You
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed">
                We understand that your time is valuable. That's why we offer extended hours 
                and convenient scheduling options to fit your busy lifestyle.
              </p>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="font-medium text-gray-900">Monday - Friday</span>
                  <span className="text-gray-600">8:00 AM - 7:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="font-medium text-gray-900">Saturday</span>
                  <span className="text-gray-600">8:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <span className="font-medium text-gray-900">Sunday</span>
                  <span className="text-gray-600">10:00 AM - 5:00 PM</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-green-600 hover:bg-green-700 flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Schedule Service
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Call Now
                </Button>
              </div>
            </div>
            
            <Card className="border-0 shadow-lg">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-gray-900">Emergency Service</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-center">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full">
                  <Phone className="h-10 w-10 text-red-600" />
                </div>
                <p className="text-gray-600">
                  Need immediate assistance? Our emergency service line is available 24/7 
                  for roadside assistance and urgent repairs.
                </p>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-red-600">(555) 911-AUTO</div>
                  <div className="text-sm text-gray-500">Available 24/7</div>
                </div>
                <Button variant="outline" className="w-full border-red-600 text-red-600 hover:bg-red-50">
                  Call Emergency Line
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">Ready to Get Started?</h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Whether you're looking for a new vehicle, need service for your current car, 
              or want to explore financing options, our team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-green-600 hover:bg-green-700 text-lg px-8 py-3">
                Browse Inventory
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900 text-lg px-8 py-3">
                Schedule Service
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
