import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, Eye, Fuel, Calendar, Settings, Users } from "lucide-react"
import Image from "next/image"

interface CarCardProps {
  car: {
    id: string
    name: string
    brand: string
    year: number
    price: number
    mileage: number
    fuelType: string
    transmission: string
    seats: number
    image: string
    condition: "new" | "used" | "certified"
    featured?: boolean
  }
}

export function CarCard({ car }: CarCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  }

  const formatMileage = (mileage: number) => {
    return new Intl.NumberFormat('en-US').format(mileage)
  }

  return (
    <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300 border-0 shadow-md">
      {/* Image Container */}
      <div className="relative overflow-hidden">
        <Image
          src={car.image}
          alt={`${car.brand} ${car.name}`}
          width={400}
          height={250}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {car.featured && (
            <Badge className="bg-yellow-500 text-black font-semibold">
              Featured
            </Badge>
          )}
          <Badge 
            variant={car.condition === "new" ? "default" : car.condition === "certified" ? "secondary" : "outline"}
            className={
              car.condition === "new"
                ? "bg-blue-600 text-white"
                : car.condition === "certified"
                ? "bg-purple-600 text-white"
                : "bg-gray-600 text-white"
            }
          >
            {car.condition.charAt(0).toUpperCase() + car.condition.slice(1)}
          </Badge>
        </div>

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button size="sm" variant="secondary" className="h-8 w-8 p-0 bg-white/90 hover:bg-white">
            <Heart className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="secondary" className="h-8 w-8 p-0 bg-white/90 hover:bg-white">
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Car Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
              {car.brand} {car.name}
            </h3>
            <span className="text-sm text-gray-500">{car.year}</span>
          </div>
          
          <div className="text-2xl font-bold text-blue-600">
            {formatPrice(car.price)}
          </div>

          {/* Specifications */}
          <div className="grid grid-cols-2 gap-3 pt-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Settings className="h-4 w-4" />
              <span>{car.transmission}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Fuel className="h-4 w-4" />
              <span>{car.fuelType}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>{formatMileage(car.mileage)} mi</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Users className="h-4 w-4" />
              <span>{car.seats} seats</span>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex gap-2">
        <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
          View Details
        </Button>
        <Button variant="outline" className="flex-1 border-blue-600 text-blue-600 hover:bg-blue-50">
          Test Drive
        </Button>
      </CardFooter>
    </Card>
  )
}
