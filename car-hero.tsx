import { But<PERSON> } from "@/components/ui/button"
import { Play, ArrowRight } from "lucide-react"
import { Navbar } from "./navbar"

export default function Component() {
  return (
    <section className="relative h-screen w-full overflow-hidden">
      {/* Navigation */}
      <Navbar />

      {/* Background Video */}
      <video autoPlay loop muted playsInline className="absolute inset-0 h-full w-full object-cover">
        <source .mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black/40" />

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold tracking-tighter text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Experience the Future
                <span className="block text-blue-400">of Automotive</span>
              </h1>
              <p className="mx-auto max-w-[700px] text-lg text-gray-200 md:text-xl lg:text-2xl">
                Discover cutting-edge vehicles that redefine performance, luxury, and innovation. Your journey to
                extraordinary begins here.
              </p>
            </div>

            <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
              <Button size="lg" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 text-lg">
                Explore Collection
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg bg-transparent"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">500+</div>
                <div className="text-sm text-gray-300 md:text-base">Premium Models</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">50+</div>
                <div className="text-sm text-gray-300 md:text-base">Global Brands</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">24/7</div>
                <div className="text-sm text-gray-300 md:text-base">Expert Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
        <div className="flex flex-col items-center space-y-2">
          <div className="h-8 w-5 rounded-full border-2 border-white">
            <div className="mx-auto mt-2 h-2 w-1 animate-pulse rounded-full bg-white"></div>
          </div>
          <span className="text-xs text-white">Scroll</span>
        </div>
      </div>
    </section>
  )
}
