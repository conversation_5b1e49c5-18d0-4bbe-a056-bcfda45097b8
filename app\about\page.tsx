import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Award, 
  Users, 
  Car, 
  Shield, 
  Heart, 
  Star, 
  CheckCircle, 
  Clock,
  Target,
  Handshake
} from "lucide-react"
import Image from "next/image"

export default function AboutPage() {
  const stats = [
    { icon: Car, label: "Premium Models", value: "500+" },
    { icon: Users, label: "Satisfied Clients", value: "10,000+" },
    { icon: Award, label: "Global Brands", value: "50+" },
    { icon: Star, label: "Excellence Rating", value: "4.9/5" }
  ]

  const values = [
    {
      icon: Shield,
      title: "Excellence & Innovation",
      description: "We curate only the finest vehicles that represent the cutting edge of automotive technology and luxury craftsmanship."
    },
    {
      icon: Heart,
      title: "Personalized Service",
      description: "Every client receives bespoke attention from our specialists, ensuring a tailored experience that exceeds expectations."
    },
    {
      icon: CheckCircle,
      title: "Uncompromising Quality",
      description: "Our rigorous selection process ensures that every vehicle meets the highest standards of performance and luxury."
    },
    {
      icon: Handshake,
      title: "Lasting Partnerships",
      description: "We build enduring relationships with our clients, providing ongoing support and exclusive access to the latest innovations."
    }
  ]

  const team = [
    {
      name: "Alexander Sterling",
      role: "Founder & CEO",
      image: "/placeholder-user.jpg",
      description: "Visionary leader with 25+ years in luxury automotive, dedicated to redefining the premium vehicle experience."
    },
    {
      name: "Victoria Laurent",
      role: "Chief Automotive Specialist",
      image: "/placeholder-user.jpg",
      description: "Expert in luxury and performance vehicles, ensuring each client finds their perfect automotive match."
    },
    {
      name: "Marcus Thompson",
      role: "Director of Innovation",
      image: "/placeholder-user.jpg",
      description: "Pioneering the integration of cutting-edge technology and sustainable luxury in our vehicle offerings."
    },
    {
      name: "Elena Rodriguez",
      role: "Client Experience Director",
      image: "/placeholder-user.jpg",
      description: "Orchestrating exceptional client journeys from initial consultation to long-term relationship management."
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <Badge className="bg-white/20 text-white border-white/30">
              Automotive Excellence
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold">
              About AutoLux
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              Pioneering the future of automotive luxury and performance, where cutting-edge
              technology meets unparalleled sophistication.
            </p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <stat.icon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <Badge className="bg-blue-100 text-blue-800">Our Story</Badge>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  Pioneering Automotive Innovation
                </h2>
              </div>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  AutoLux was born from a vision to redefine the automotive experience. We specialize in
                  cutting-edge vehicles that represent the pinnacle of luxury, performance, and innovation.
                  Our curated collection features the world's most prestigious automotive brands.
                </p>
                <p>
                  We believe that a vehicle is more than transportation – it's an expression of your
                  lifestyle and aspirations. Our team of automotive specialists works tirelessly to
                  match each client with their perfect vehicle, ensuring an experience that exceeds
                  expectations at every turn.
                </p>
                <p>
                  Today, we're proud to serve discerning clients who demand excellence. With over 500
                  premium models from 50+ global brands, we continue to set new standards in automotive
                  luxury and customer service.
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Explore Collection
                </Button>
                <Button variant="outline">
                  Contact Us
                </Button>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.jpg"
                alt="Baligh Motors Showroom"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg">
                <div className="flex items-center gap-3">
                  <Clock className="h-8 w-8 text-green-600" />
                  <div>
                    <div className="font-semibold text-gray-900">15+ Years</div>
                    <div className="text-sm text-gray-600">Serving Our Community</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-blue-100 text-blue-800">Our Values</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              What Drives Us Forward
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our core values guide every interaction and decision we make, ensuring 
              exceptional experiences for every customer.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center border-0 shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="p-6 space-y-4">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                    <value.icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-purple-100 text-purple-800">Our Team</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Automotive Specialists
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our team of passionate automotive experts brings decades of experience in luxury
              and performance vehicles to guide your journey to automotive excellence.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center border-0 shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="p-6 space-y-4">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={120}
                    height={120}
                    className="w-24 h-24 rounded-full mx-auto object-cover"
                  />
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">{member.name}</h3>
                    <p className="text-blue-600 font-medium">{member.role}</p>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-600 rounded-full">
              <Target className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold">Our Mission</h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              To redefine the automotive experience by curating the world's finest vehicles and
              delivering unparalleled service. We are committed to innovation, excellence, and
              creating lasting partnerships with clients who share our passion for automotive perfection.
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3">
              Experience Excellence
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
