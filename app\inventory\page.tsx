"use client"

import { useState } from "react"
import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { CarCard } from "@/components/car-card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Filter, Grid, List } from "lucide-react"

// Sample car data
const cars = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    brand: "Toyota",
    year: 2023,
    price: 28500,
    mileage: 15000,
    fuelType: "Hybrid",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "used" as const,
    featured: true
  },
  {
    id: "2",
    name: "Accord",
    brand: "Honda",
    year: 2024,
    price: 32000,
    mileage: 5000,
    fuelType: "Gasoline",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "certified" as const
  },
  {
    id: "3",
    name: "Model 3",
    brand: "Tesla",
    year: 2024,
    price: 45000,
    mileage: 0,
    fuelType: "Electric",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "new" as const,
    featured: true
  },
  {
    id: "4",
    name: "X5",
    brand: "BMW",
    year: 2022,
    price: 55000,
    mileage: 25000,
    fuelType: "Gasoline",
    transmission: "Automatic",
    seats: 7,
    image: "/placeholder.jpg",
    condition: "used" as const
  },
  {
    id: "5",
    name: "F-150",
    brand: "Ford",
    year: 2023,
    price: 42000,
    mileage: 12000,
    fuelType: "Gasoline",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "certified" as const
  },
  {
    id: "6",
    name: "Prius",
    brand: "Toyota",
    year: 2024,
    price: 27000,
    mileage: 0,
    fuelType: "Hybrid",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "new" as const
  }
]

export default function ModelsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedCondition, setSelectedCondition] = useState("all")
  const [selectedFuelType, setSelectedFuelType] = useState("all")
  const [priceRange, setPriceRange] = useState([0, 100000])
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("price-low")

  // Get unique brands
  const brands = Array.from(new Set(cars.map(car => car.brand))).sort()

  // Filter cars based on criteria
  const filteredCars = cars.filter(car => {
    const matchesSearch = car.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         car.brand.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesBrand = selectedBrand === "all" || car.brand === selectedBrand
    const matchesCondition = selectedCondition === "all" || car.condition === selectedCondition
    const matchesFuelType = selectedFuelType === "all" || car.fuelType === selectedFuelType
    const matchesPrice = car.price >= priceRange[0] && car.price <= priceRange[1]

    return matchesSearch && matchesBrand && matchesCondition && matchesFuelType && matchesPrice
  })

  // Sort cars
  const sortedCars = [...filteredCars].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "year-new":
        return b.year - a.year
      case "year-old":
        return a.year - b.year
      case "mileage-low":
        return a.mileage - b.mileage
      case "mileage-high":
        return b.mileage - a.mileage
      default:
        return 0
    }
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900">Premium Models</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our curated collection of luxury and performance vehicles. Discover cutting-edge models from the world's finest automotive brands.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-80">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Search */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by make or model..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Brand Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Brand</label>
                  <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Brands" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Brands</SelectItem>
                      {brands.map(brand => (
                        <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Condition Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Condition</label>
                  <Select value={selectedCondition} onValueChange={setSelectedCondition}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Conditions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Conditions</SelectItem>
                      <SelectItem value="new">New</SelectItem>
                      <SelectItem value="certified">Certified Pre-Owned</SelectItem>
                      <SelectItem value="used">Used</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Fuel Type Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Fuel Type</label>
                  <Select value={selectedFuelType} onValueChange={setSelectedFuelType}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Fuel Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Fuel Types</SelectItem>
                      <SelectItem value="Gasoline">Gasoline</SelectItem>
                      <SelectItem value="Hybrid">Hybrid</SelectItem>
                      <SelectItem value="Electric">Electric</SelectItem>
                      <SelectItem value="Diesel">Diesel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range */}
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Price Range: ${priceRange[0].toLocaleString()} - ${priceRange[1].toLocaleString()}
                  </label>
                  <Slider
                    value={priceRange}
                    onValueChange={setPriceRange}
                    max={100000}
                    min={0}
                    step={1000}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {sortedCars.length} Vehicle{sortedCars.length !== 1 ? 's' : ''} Found
                </h2>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                    <SelectItem value="year-new">Year: Newest First</SelectItem>
                    <SelectItem value="year-old">Year: Oldest First</SelectItem>
                    <SelectItem value="mileage-low">Mileage: Low to High</SelectItem>
                    <SelectItem value="mileage-high">Mileage: High to Low</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Cars Grid */}
            <div className={`grid gap-6 ${viewMode === "grid" ? "grid-cols-1 md:grid-cols-2 xl:grid-cols-3" : "grid-cols-1"}`}>
              {sortedCars.map(car => (
                <CarCard key={car.id} car={car} />
              ))}
            </div>

            {/* No Results */}
            {sortedCars.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">No vehicles found</div>
                <div className="text-gray-500">Try adjusting your search criteria</div>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
