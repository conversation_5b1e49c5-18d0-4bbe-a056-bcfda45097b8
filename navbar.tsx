"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, Search, User, Phone } from "lucide-react"

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <nav className="absolute top-0 left-0 right-0 z-20 px-4 py-4 md:px-6 lg:px-8">
      <div className="container mx-auto">
        {/* Glass morphism background */}
        <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl px-6 py-4 shadow-xl">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-white font-bold text-xl">A</span>
              </div>
              <span className="text-white font-bold text-xl hidden sm:block">AutoLux</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium">
                Models
              </a>
              <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium">
                Services
              </a>
              <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium">
                Financing
              </a>
              <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium">
                About
              </a>
              <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium">
                Contact
              </a>
            </div>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              {/* Search */}
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 rounded-xl hidden md:flex">
                <Search className="h-5 w-5" />
              </Button>

              {/* Contact */}
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 rounded-xl hidden md:flex">
                <Phone className="h-4 w-4 mr-2" />
                Call Us
              </Button>

              {/* Account */}
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 rounded-xl hidden sm:flex">
                <User className="h-4 w-4 mr-2" />
                Account
              </Button>

              {/* CTA Button */}
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl px-6 hidden sm:flex">
                Book Test Drive
              </Button>

              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 rounded-xl lg:hidden"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden mt-4 pt-4 border-t border-white/20">
              <div className="flex flex-col space-y-4">
                <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium py-2">
                  Models
                </a>
                <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium py-2">
                  Services
                </a>
                <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium py-2">
                  Financing
                </a>
                <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium py-2">
                  About
                </a>
                <a href="#" className="text-white/90 hover:text-white transition-colors duration-200 font-medium py-2">
                  Contact
                </a>
                <div className="flex flex-col space-y-3 pt-4 border-t border-white/20">
                  <Button variant="ghost" className="text-white hover:bg-white/20 rounded-xl justify-start">
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                  <Button variant="ghost" className="text-white hover:bg-white/20 rounded-xl justify-start">
                    <User className="h-4 w-4 mr-2" />
                    Account
                  </Button>
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl">
                    Book Test Drive
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
