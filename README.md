# Baligh Motors - Car Dealership Website

A modern, responsive car dealership website built with Next.js, TypeScript, and Tailwind CSS. This project showcases a complete automotive business solution with inventory management, financing tools, and customer engagement features.

## 🚗 Features

### Core Pages
- **Home Page** - Hero section with company branding and featured vehicles
- **Inventory Page** - Browse vehicles with advanced filtering and search
- **Car Details Page** - Detailed vehicle information with image gallery
- **About Us Page** - Company history, team, and values
- **Services Page** - Comprehensive service offerings
- **Contact Page** - Multiple contact forms and location information
- **Financing Page** - Loan calculator and financing options
- **Test Drive Booking** - Schedule test drives with calendar integration

### Key Features
- 🎨 **Modern Design** - Clean, professional interface with Baligh Motors branding
- 📱 **Responsive Layout** - Optimized for desktop, tablet, and mobile devices
- 🔍 **Advanced Search** - Filter cars by brand, price, condition, fuel type, and more
- 💰 **Loan Calculator** - Interactive financing calculator with real-time updates
- 📅 **Test Drive Booking** - Calendar-based appointment scheduling
- ⭐ **Customer Reviews** - Testimonials and rating system
- 🛡️ **Quality Assurance** - Vehicle history and warranty information
- 📞 **Multiple Contact Points** - Department-specific contact information

## 🛠️ Technology Stack

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Form Handling**: React Hook Form with Zod validation

## 📁 Project Structure

```
baligh-motors/
├── app/                    # Next.js app directory
│   ├── about/             # About us page
│   ├── car/[id]/          # Dynamic car details page
│   ├── contact/           # Contact page
│   ├── financing/         # Financing and loan calculator
│   ├── inventory/         # Vehicle inventory with filters
│   ├── services/          # Services page
│   ├── test-drive/        # Test drive booking
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── ui/               # UI component library
│   ├── car-card.tsx      # Vehicle display card
│   └── footer.tsx        # Site footer
├── car-hero.tsx          # Hero section component
├── navbar.tsx            # Navigation component
└── public/               # Static assets
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd baligh-motors
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 🎨 Customization

### Branding
The website is currently branded for "Baligh Motors" but can be easily customized:

1. **Logo and Company Name**: Update in `navbar.tsx` and `footer.tsx`
2. **Colors**: Modify the color scheme in `tailwind.config.ts`
3. **Content**: Update text content in each page component
4. **Images**: Replace placeholder images in the `public/` directory

### Adding Vehicles
Vehicle data is currently hardcoded in components. To integrate with a database:

1. Create API routes in `app/api/`
2. Update components to fetch data from APIs
3. Implement CRUD operations for inventory management

## 📊 Features Overview

### Inventory Management
- Advanced filtering by multiple criteria
- Grid and list view options
- Sorting by price, year, mileage
- Vehicle condition badges (New, Certified, Used)

### Financing Tools
- Interactive loan calculator
- Real-time payment calculations
- Multiple financing options
- Credit score-based rate tiers

### Customer Engagement
- Test drive scheduling with calendar
- Multiple contact forms
- Department-specific contact information
- Customer testimonials and reviews

### Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interface
- Fast loading times

## 🔧 Development

### Adding New Pages
1. Create a new directory in `app/`
2. Add a `page.tsx` file
3. Update navigation in `navbar.tsx`

### Styling Guidelines
- Use Tailwind CSS utility classes
- Follow the existing color scheme (blue and green gradients)
- Maintain consistent spacing and typography
- Ensure responsive design principles

### Component Development
- Create reusable components in `components/`
- Use TypeScript for type safety
- Follow the existing naming conventions
- Include proper prop types and documentation

## 📝 License

This project is created for demonstration purposes. Feel free to use and modify as needed.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or support regarding this project, please contact the development team.

---

**Baligh Motors** - Your Trusted Automotive Partner
