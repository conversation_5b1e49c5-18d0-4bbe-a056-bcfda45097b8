import Component from "../car-hero"
import { Footer } from "@/components/footer"
import { CarCard } from "@/components/car-card"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Star,
  ArrowRight,
  Quote,
  CheckCircle,
  Car,
  Wrench,
  CreditCard
} from "lucide-react"

// Sample featured cars
const featuredCars = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    brand: "Toyota",
    year: 2023,
    price: 28500,
    mileage: 15000,
    fuelType: "Hybrid",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "used" as const,
    featured: true
  },
  {
    id: "2",
    name: "Model 3",
    brand: "Tesla",
    year: 2024,
    price: 45000,
    mileage: 0,
    fuelType: "Electric",
    transmission: "Automatic",
    seats: 5,
    image: "/placeholder.jpg",
    condition: "new" as const,
    featured: true
  },
  {
    id: "3",
    name: "X5",
    brand: "BMW",
    year: 2022,
    price: 55000,
    mileage: 25000,
    fuelType: "Gasoline",
    transmission: "Automatic",
    seats: 7,
    image: "/placeholder.jpg",
    condition: "certified" as const
  }
]

const testimonials = [
  {
    name: "Sarah Johnson",
    rating: 5,
    text: "Exceptional service from start to finish. The team at Baligh Motors made buying my first car a breeze!",
    vehicle: "2023 Honda Accord"
  },
  {
    name: "Mike Rodriguez",
    rating: 5,
    text: "Honest pricing, quality vehicles, and outstanding customer service. I'll definitely be back for my next car.",
    vehicle: "2022 Toyota Prius"
  },
  {
    name: "Lisa Chen",
    rating: 5,
    text: "The financing process was smooth and transparent. They found me a great rate and explained everything clearly.",
    vehicle: "2024 Ford F-150"
  }
]

const services = [
  {
    icon: Car,
    title: "Vehicle Sales",
    description: "Browse our extensive inventory of quality new and pre-owned vehicles."
  },
  {
    icon: Wrench,
    title: "Service & Maintenance",
    description: "Professional maintenance and repair services to keep your car running smoothly."
  },
  {
    icon: CreditCard,
    title: "Financing Solutions",
    description: "Flexible financing options with competitive rates for all credit types."
  }
]

export default function Page() {
  return (
    <div>
      <Component />

      {/* Featured Vehicles Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-blue-100 text-blue-800">Featured Models</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Premium Collection
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our handpicked selection of luxury and performance vehicles,
              each representing the pinnacle of automotive excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {featuredCars.map(car => (
              <CarCard key={car.id} car={car} />
            ))}
          </div>

          <div className="text-center">
            <Button className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3">
              Explore All Models
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-blue-100 text-blue-800">Our Services</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Complete Automotive Solutions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From sales to service, we're your one-stop destination for all automotive needs.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow text-center">
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4 mx-auto">
                    <service.icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl text-gray-900">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed mb-4">{service.description}</p>
                  <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <Badge className="bg-purple-100 text-purple-800">Why Choose AutoLux</Badge>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  The Future of Automotive Excellence
                </h2>
              </div>
              <p className="text-lg text-gray-600 leading-relaxed">
                We specialize in cutting-edge vehicles that combine luxury, performance, and innovation.
                Experience the difference that comes with choosing automotive excellence.
              </p>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-6 w-6 text-blue-600 flex-shrink-0" />
                  <span className="text-gray-700">Cutting-edge automotive technology</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-6 w-6 text-blue-600 flex-shrink-0" />
                  <span className="text-gray-700">Premium luxury and performance vehicles</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-6 w-6 text-blue-600 flex-shrink-0" />
                  <span className="text-gray-700">Expert guidance from automotive specialists</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-6 w-6 text-blue-600 flex-shrink-0" />
                  <span className="text-gray-700">Comprehensive service and support</span>
                </div>
              </div>

              <Button className="bg-blue-600 hover:bg-blue-700">
                Learn More About Us
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="text-center p-6 bg-blue-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
                <div className="text-gray-600">Premium Models</div>
              </div>
              <div className="text-center p-6 bg-purple-50 rounded-lg">
                <div className="text-3xl font-bold text-purple-600 mb-2">50+</div>
                <div className="text-gray-600">Global Brands</div>
              </div>
              <div className="text-center p-6 bg-indigo-50 rounded-lg">
                <div className="text-3xl font-bold text-indigo-600 mb-2">24/7</div>
                <div className="text-gray-600">Expert Support</div>
              </div>
              <div className="text-center p-6 bg-cyan-50 rounded-lg">
                <div className="text-3xl font-bold text-cyan-600 mb-2">4.9/5</div>
                <div className="text-gray-600">Customer Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-12">
            <Badge className="bg-yellow-100 text-yellow-800">Customer Reviews</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied customers
              have to say about their experience with AutoLux.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-md">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <Quote className="h-8 w-8 text-gray-300 mb-4" />
                  <p className="text-gray-600 leading-relaxed mb-4">{testimonial.text}</p>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.vehicle}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">Ready to Experience the Future?</h2>
            <p className="text-xl text-blue-100 leading-relaxed">
              Explore our premium collection, schedule a test drive, or speak with our automotive specialists.
              Your journey to extraordinary begins here.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3">
                Explore Collection
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-3">
                Schedule Test Drive
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
