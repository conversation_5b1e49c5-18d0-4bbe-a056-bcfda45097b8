"use client"

import { useState } from "react"
import { Navbar } from "@/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  MessageSquare,
  Send,
  Car,
  Wrench,
  CreditCard
} from "lucide-react"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: ""
    })
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const contactInfo = [
    {
      icon: Phone,
      title: "Phone",
      details: [
        { label: "Sales", value: "(*************" },
        { label: "Service", value: "(*************" },
        { label: "Parts", value: "(*************" }
      ]
    },
    {
      icon: Mail,
      title: "Email",
      details: [
        { label: "General", value: "<EMAIL>" },
        { label: "Sales", value: "<EMAIL>" },
        { label: "Service", value: "<EMAIL>" }
      ]
    },
    {
      icon: MapPin,
      title: "Location",
      details: [
        { label: "Address", value: "123 Auto Plaza Drive" },
        { label: "City", value: "Automotive City, AC 12345" },
        { label: "Directions", value: "Get Directions" }
      ]
    },
    {
      icon: Clock,
      title: "Hours",
      details: [
        { label: "Mon-Fri", value: "9:00 AM - 8:00 PM" },
        { label: "Saturday", value: "9:00 AM - 6:00 PM" },
        { label: "Sunday", value: "11:00 AM - 5:00 PM" }
      ]
    }
  ]

  const departments = [
    {
      icon: Car,
      title: "Sales Department",
      description: "Find your perfect vehicle with our expert sales team.",
      phone: "(*************",
      email: "<EMAIL>"
    },
    {
      icon: Wrench,
      title: "Service Department",
      description: "Keep your vehicle running smoothly with professional service.",
      phone: "(*************",
      email: "<EMAIL>"
    },
    {
      icon: CreditCard,
      title: "Finance Department",
      description: "Explore financing options and get pre-approved today.",
      phone: "(*************",
      email: "<EMAIL>"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-green-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <Badge className="bg-white/20 text-white border-white/30">
              Get In Touch
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              Have questions? Need assistance? Our friendly team is here to help you 
              with all your automotive needs.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Cards */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {contactInfo.map((info, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow text-center">
                <CardHeader className="pb-4">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4 mx-auto">
                    <info.icon className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-xl text-gray-900">{info.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {info.details.map((detail, detailIndex) => (
                    <div key={detailIndex} className="text-sm">
                      <span className="text-gray-500">{detail.label}:</span>
                      <span className="text-gray-900 ml-1 font-medium">{detail.value}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900 flex items-center gap-2">
                  <MessageSquare className="h-6 w-6 text-green-600" />
                  Send us a Message
                </CardTitle>
                <p className="text-gray-600">
                  Fill out the form below and we'll get back to you as soon as possible.
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Full Name *
                      </label>
                      <Input
                        required
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Phone Number
                      </label>
                      <Input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        placeholder="Your phone number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Subject *
                    </label>
                    <Select value={formData.subject} onValueChange={(value) => handleInputChange("subject", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sales">Sales Inquiry</SelectItem>
                        <SelectItem value="service">Service Appointment</SelectItem>
                        <SelectItem value="financing">Financing Question</SelectItem>
                        <SelectItem value="parts">Parts Request</SelectItem>
                        <SelectItem value="general">General Question</SelectItem>
                        <SelectItem value="feedback">Feedback</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Message *
                    </label>
                    <Textarea
                      required
                      rows={5}
                      value={formData.message}
                      onChange={(e) => handleInputChange("message", e.target.value)}
                      placeholder="Tell us how we can help you..."
                    />
                  </div>

                  <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-lg py-3">
                    <Send className="h-5 w-5 mr-2" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Departments */}
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact by Department</h2>
                <p className="text-gray-600 mb-6">
                  Reach out to the right department for faster, more specialized assistance.
                </p>
              </div>

              <div className="space-y-4">
                {departments.map((dept, index) => (
                  <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg flex-shrink-0">
                          <dept.icon className="h-6 w-6 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{dept.title}</h3>
                          <p className="text-gray-600 text-sm mb-3">{dept.description}</p>
                          <div className="space-y-1 text-sm">
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-900">{dept.phone}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-900">{dept.email}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Emergency Contact */}
              <Card className="border-2 border-red-200 bg-red-50">
                <CardContent className="p-6 text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                    <Phone className="h-8 w-8 text-red-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-red-900 mb-2">Emergency Roadside Assistance</h3>
                  <p className="text-red-700 text-sm mb-4">
                    Need immediate help? Our 24/7 emergency line is always available.
                  </p>
                  <div className="text-2xl font-bold text-red-600 mb-4">(555) 911-AUTO</div>
                  <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-100">
                    Call Emergency Line
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Visit Our Showroom</h2>
            <p className="text-lg text-gray-600">
              Come see our vehicles in person and meet our friendly team.
            </p>
          </div>
          
          {/* Placeholder for map */}
          <div className="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center space-y-2">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto" />
              <div className="text-gray-600">Interactive Map</div>
              <div className="text-sm text-gray-500">123 Auto Plaza Drive, Automotive City, AC 12345</div>
              <Button className="bg-green-600 hover:bg-green-700 mt-4">
                Get Directions
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
